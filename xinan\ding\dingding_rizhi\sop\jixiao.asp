<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->

<%
'获取URL参数
riqi = Request.QueryString("riqi")
xingming = Request.QueryString("xingming")
zhongwen_xingming = Request.QueryString("zhongwen_xingming")
guoqi_renwu_shuliang = Request.QueryString("guoqi_renwu_shuliang")
koufen = Request.QueryString("koufen")
koufen_mingxi = Request.QueryString("koufen_mingxi")
koufen_xitong = Request.QueryString("koufen_xitong")

'检查必填参数
If xingming = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少姓名参数""}"
    Response.End
End If

'构建插入SQL
Dim insert_sql
Dim insert_fields()
Dim insert_values()
Dim field_count
field_count = 0

'动态构建插入字段和值
If riqi <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[日期]"
    insert_values(field_count) = "'" & riqi & "'"
    field_count = field_count + 1
End If

If xingming <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[姓名]"
    insert_values(field_count) = "'" & xingming & "'"
    field_count = field_count + 1
End If

If zhongwen_xingming <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[中文姓名]"
    insert_values(field_count) = "'" & zhongwen_xingming & "'"
    field_count = field_count + 1
End If

If guoqi_renwu_shuliang <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[过期任务数量]"
    insert_values(field_count) = guoqi_renwu_shuliang
    field_count = field_count + 1
End If

If koufen <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[扣分]"
    insert_values(field_count) = koufen
    field_count = field_count + 1
End If

If koufen_mingxi <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[扣分明细]"
    insert_values(field_count) = "'" & koufen_mingxi & "'"
    field_count = field_count + 1
End If

If koufen_xitong <> "" Then
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[扣分系统]"
    insert_values(field_count) = "'" & koufen_xitong & "'"
    field_count = field_count + 1
End If

'检查是否有字段需要插入
If field_count = 0 Then
    Response.Write "{""code"":-1,""msg"":""没有需要插入的字段""}"
    Response.End
End If

'构建完整的插入SQL
insert_sql = "INSERT INTO [待办任务扣分表] (" & Join(insert_fields, ", ") & ") VALUES (" & Join(insert_values, ", ") & ")"

'执行插入操作
On Error Resume Next
conn.Execute insert_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""插入失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

' 返回成功响应
Response.Write "{""code"":1,""msg"":""添加记录成功""}"


%>
